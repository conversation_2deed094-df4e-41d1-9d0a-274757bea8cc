import { useApiGenerationWithRetry } from 'src/hooks/useApiGenerationWithRetry';

import { useGenerateTitleMutation } from 'src/services/apis/generateContentApi';

/**
 * Custom hook for generating article titles with retry functionality
 * @returns Functions and state for title generation
 */
export const useTitleGeneration = () => {
  // Use the RTK Query mutation hook
  const [generateTitle, { isLoading, isError, error }] = useGenerateTitleMutation();

  // Use retry mechanism for API calls
  const retryHandler = useApiGenerationWithRetry({
    maxRetries: 3,
    retryDelay: 2000, // 2 seconds delay between retries
  });

  /**
   * Generate a title based on keywords and content description with retry functionality
   * @param primaryKeyword - The main keyword for the article
   * @param secondaryKeywords - Array of secondary keywords
   * @param contentDescription - Description of the article content
   * @param language - Target language for title generation (defaults to 'english')
   * @returns The generated title or null if there was an error
   */
  const generateArticleTitle = async (
    primaryKeyword: string,
    secondaryKeywords: string[] = [],
    contentDescription: string = '',
    language: string = 'english'
  ): Promise<string | null> => {
    // Create the API call function
    const apiCall = async () => {
      // Prepare the request payload with language parameter
      const payload = {
        primary_keyword: primaryKeyword,
        secondary_keywords: secondaryKeywords,
        content_description: contentDescription,
        language: language || 'english' // Default to English if no language provided
      };

      // Call the API
      const response = await generateTitle(payload).unwrap();

      // Check if the request was successful
      if (response.success && response.titles && response.titles.length > 0) {
        const randomIndexNumber = Math.floor(Math.random() * response.titles.length);
        const firstTitle = response.titles[randomIndexNumber];
        return firstTitle;
      }

      throw new Error(response.message || 'Title generation failed');
    };

    try {
      // Execute with retry mechanism
      return await retryHandler.executeWithRetry(
        apiCall,
        'Title Generation',
        `Failed to generate title for "${primaryKeyword}". Server might be overloaded.`
      );
    } catch (err) {
      console.error('❌ Final error generating title:', err);
      return null;
    }
  };

  return {
    generateArticleTitle,
    isGenerating: isLoading || retryHandler.isLoading,
    isRetrying: retryHandler.isRetrying,
    isError,
    error: error || retryHandler.error,
    retryCount: retryHandler.retryCount,
    canRetry: retryHandler.canRetry,
    retry: retryHandler.retry,
    reset: retryHandler.reset,
  };
};

/**
 * Parse a title generation API response
 * @param response - The API response from the title generation endpoint
 * @returns The generated title or null if unsuccessful
 */
export const parseTitleResponse = (response: {
  titles: string[];
  success: boolean;
  message: string;
}): string | null => {
  if (!response.success) {
    console.error('Title generation failed:', response.message);
    return null;
  }

  if (!response.titles || response.titles.length === 0) {
    console.error('No titles returned in response');
    return null;
  }

  // Return the first title from the array
  return response.titles[0];
};
