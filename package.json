{"name": "xblog-ai", "author": "xblog.ai", "licence": "MIT", "version": "1.0.0", "private": false, "type": "module", "scripts": {"dev": "vite", "start": "vite preview", "build": "tsc && vite build", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "fm:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx}\"", "fm:fix": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "rm:all": "rm -rf node_modules .next out dist build", "re:start": "yarn rm:all && yarn install && yarn dev", "re:build": "yarn rm:all && yarn install && yarn build", "re:build-npm": "npm run rm:all && npm install && npm run build", "dev:host": "vite --host"}, "engines": {"node": "20.x"}, "packageManager": "yarn@1.22.22", "dependencies": {"@emotion/cache": "^11.13.1", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@firebase/analytics": "^0.10.18", "@fontsource-variable/dm-sans": "^5.0.7", "@fontsource/barlow": "^5.0.14", "@hookform/resolvers": "^5.0.1", "@iconify/react": "^5.0.2", "@mui/icons-material": "^6.4.8", "@mui/lab": "^5.0.0-alpha.173", "@mui/material": "^5.16.7", "@mui/x-date-pickers": "^7.27.1", "@react-oauth/google": "^0.12.1", "@reduxjs/toolkit": "^2.5.1", "@tanstack/react-query": "^5.74.4", "@tinymce/tinymce-react": "^6.1.0", "@tiptap/core": "^2.11.7", "@tiptap/extension-blockquote": "^2.11.7", "@tiptap/extension-bold": "^2.11.7", "@tiptap/extension-bullet-list": "^2.11.7", "@tiptap/extension-code": "^2.11.7", "@tiptap/extension-code-block": "^2.11.7", "@tiptap/extension-color": "^2.11.7", "@tiptap/extension-document": "^2.11.7", "@tiptap/extension-dropcursor": "^2.11.7", "@tiptap/extension-font-family": "^2.11.7", "@tiptap/extension-gapcursor": "^2.11.7", "@tiptap/extension-hard-break": "^2.11.7", "@tiptap/extension-highlight": "^2.11.7", "@tiptap/extension-history": "^2.11.7", "@tiptap/extension-horizontal-rule": "^2.11.7", "@tiptap/extension-image": "^2.11.7", "@tiptap/extension-italic": "^2.11.7", "@tiptap/extension-link": "^2.11.7", "@tiptap/extension-list-item": "^2.11.7", "@tiptap/extension-ordered-list": "^2.11.7", "@tiptap/extension-paragraph": "^2.11.7", "@tiptap/extension-placeholder": "^2.11.7", "@tiptap/extension-strike": "^2.11.7", "@tiptap/extension-subscript": "^2.11.7", "@tiptap/extension-superscript": "^2.11.7", "@tiptap/extension-table": "^2.11.7", "@tiptap/extension-table-cell": "^2.11.7", "@tiptap/extension-table-header": "^2.11.7", "@tiptap/extension-table-row": "^2.11.7", "@tiptap/extension-task-item": "^2.11.7", "@tiptap/extension-task-list": "^2.11.7", "@tiptap/extension-text": "^2.11.7", "@tiptap/extension-text-align": "^2.11.7", "@tiptap/extension-text-style": "^2.11.7", "@tiptap/extension-underline": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@types/jspdf": "^1.3.3", "@types/react-beautiful-dnd": "^13.1.8", "apexcharts": "^3.52.0", "axios": "^1.7.9", "axios-mock-adapter": "^2.1.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "firebase": "^12.0.0", "framer-motion": "^12.15.0", "history": "^5.3.0", "js-file-download": "^0.4.12", "jspdf": "^3.0.1", "lottie-react": "^2.4.1", "lucide-react": "^0.475.0", "mui-tiptap": "^1.18.1", "notistack": "^3.0.2", "react": "^18.3.1", "react-apexcharts": "^1.4.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-error-boundary": "^5.0.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.56.0", "react-hook-form-mui": "^7.6.0", "react-hot-toast": "^2.5.2", "react-redux": "^9.2.0", "react-router-dom": "^6.26.1", "react-to-pdf": "^2.0.0", "react-world-flags": "^1.6.0", "redux-devtools-extension": "^2.13.9", "redux-persist": "^6.0.0", "simplebar-react": "^3.2.6", "zod": "^3.24.3"}, "devDependencies": {"@types/node": "^22.5.0", "@types/react": "^18.3.4", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react-swc": "^3.7.0", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.9.0", "eslint-plugin-perfectionist": "^2.11.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-unused-imports": "^3.2.0", "i18next": "^24.2.2", "prettier": "^3.3.3", "react-i18next": "^15.4.1", "typescript": "^5.5.4", "vite": "^5.4.2", "vite-plugin-checker": "^0.7.2"}}