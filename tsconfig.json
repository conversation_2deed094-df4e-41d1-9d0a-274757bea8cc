{"compilerOptions": {"baseUrl": ".", "target": "ESNext", "lib": ["DOM", "DOM.Iterable", "ESNext"], "strict": true, "noEmit": true, "allowJs": true, "module": "ESNext", "jsx": "react-jsx", "skipLibCheck": true, "noImplicitAny": true, "noImplicitThis": true, "esModuleInterop": true, "isolatedModules": true, "strictNullChecks": true, "resolveJsonModule": true, "moduleResolution": "Node", "noFallthroughCasesInSwitch": true, "useUnknownInCatchVariables": false, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true}, "include": ["src"], "exclude": ["node_modules"], "references": [{"path": "./tsconfig.node.json"}]}