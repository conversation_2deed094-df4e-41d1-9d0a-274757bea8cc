/** **************************************
* Fonts: app
*************************************** */
@import '@fontsource-variable/dm-sans';

@import '@fontsource/barlow/400.css';
@import '@fontsource/barlow/500.css';
@import '@fontsource/barlow/600.css';
@import '@fontsource/barlow/700.css';
@import '@fontsource/barlow/800.css';

/** **************************************
* Plugins
*************************************** */
/* scrollbar */
@import './components/scrollbar/styles.css';

/* chart */
@import './components/chart/styles.css';

/** **************************************
* CSS Reset for Browser Consistency
*************************************** */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/** **************************************
* Baseline
*************************************** */
:root {
  --edge-font-adjustment: 1; /* Will be set by browser-normalize.ts */
}

html {
  height: 100%;
  -webkit-overflow-scrolling: touch;
  font-size: calc(14px * var(--edge-font-adjustment)); /* Adjusted base font size with browser-specific correction */
  -webkit-text-size-adjust: 100%; /* Prevent font scaling in landscape while allowing user zoom */
  -ms-text-size-adjust: 100%; /* IE/Edge specific */
  text-size-adjust: 100%;
}

/* Zoom level specific adjustments */
html[data-browser-zoom="1.25"] {
  font-size: calc(13px * var(--edge-font-adjustment));
}

html[data-browser-zoom="1.5"] {
  font-size: calc(12px * var(--edge-font-adjustment));
}

html[data-browser-zoom="1.75"],
html[data-browser-zoom="2"] {
  font-size: calc(11px * var(--edge-font-adjustment));
}

/* Edge-specific adjustments */
@supports (-ms-ime-align:auto) {
  html {
    -ms-overflow-style: -ms-autohiding-scrollbar;
  }
}
body {
  min-height: 100%;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Force browsers to use the same rendering mode */
  -webkit-text-rendering: optimizeLegibility;
  -moz-text-rendering: optimizeLegibility;
  -ms-text-rendering: optimizeLegibility;
  -o-text-rendering: optimizeLegibility;
  /* Disable auto-enlargement of small text in Safari */
  -webkit-text-size-adjust: 100%;
}

body,
#root,
#root__layout {
  display: flex;
  flex: 1 1 auto;
  min-height: 100%;
  flex-direction: column;
}
img {
  max-width: 100%;
  vertical-align: middle;
}
ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
input[type='number'] {
  -moz-appearance: textfield;
  appearance: none;
}
input[type='number']::-webkit-outer-spin-button {
  margin: 0;
  -webkit-appearance: none;
}
input[type='number']::-webkit-inner-spin-button {
  margin: 0;
  -webkit-appearance: none;
}
