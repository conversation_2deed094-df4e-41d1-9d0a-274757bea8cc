import { jsPDF } from "jspdf";
import html2canvas from "html2canvas";

export const generatePDF = async (elementToPrintId: string) => {
  const element = document.getElementById(elementToPrintId);
  if (!element) {
    throw new Error(`Element with id ${elementToPrintId} not found`);
  }
  const canvas = await html2canvas(element, { scale: 2 });
  const data = canvas.toDataURL("image/png");
  // eslint-disable-next-line new-cap
  const pdf = new jsPDF({
    orientation: "portrait",
    unit: "mm",
    format: [129, 70],
  });
  const imgProperties = pdf.getImageProperties(data);
  const pdfWidth = pdf.internal.pageSize.getWidth();
  const pdfHeight = (imgProperties.height * pdfWidth) / imgProperties.width;

  pdf.addImage(data, "PNG", 0, 0, pdfWidth, pdfHeight);
  pdf.save("print.pdf");
};